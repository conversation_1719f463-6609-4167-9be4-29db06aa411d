{"version": 3, "file": "FsaNodeFsOpenFile.js", "sourceRoot": "", "sources": ["../../src/fsa-to-node/FsaNodeFsOpenFile.ts"], "names": [], "mappings": ";;;AAIA;;;GAGG;AACH,MAAa,iBAAiB;IAW5B,YACkB,EAAU,EACV,UAAsB,EACtB,KAAa,EACb,IAA+B,EAC/B,QAAgB;QAJhB,OAAE,GAAF,EAAE,CAAQ;QACV,eAAU,GAAV,UAAU,CAAY;QACtB,UAAK,GAAL,KAAK,CAAQ;QACb,SAAI,GAAJ,IAAI,CAA2B;QAC/B,aAAQ,GAAR,QAAQ,CAAQ;QAfxB,SAAI,GAAW,CAAC,CAAC;QAiBzB,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,KAAK,2BAAgB,CAAC,CAAC;IACpD,CAAC;IAEM,KAAK,CAAC,KAAK,KAAmB,CAAC;IAE/B,KAAK,CAAC,KAAK,CAAC,IAAqB,EAAE,IAAmB;QAC3D,IAAI,OAAO,IAAI,KAAK,QAAQ;YAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAC/C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,gBAAgB,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAC;QAC3F,MAAM,MAAM,CAAC,KAAK,CAAC;YACjB,IAAI,EAAE,OAAO;YACb,IAAI;YACJ,QAAQ,EAAE,IAAI;SACf,CAAC,CAAC;QACH,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC;IAC/B,CAAC;CACF;AAnCD,8CAmCC"}