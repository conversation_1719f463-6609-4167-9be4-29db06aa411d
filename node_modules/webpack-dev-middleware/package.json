{"name": "webpack-dev-middleware", "version": "7.4.2", "description": "A development middleware for webpack", "license": "MIT", "repository": "webpack/webpack-dev-middleware", "author": "<PERSON> @sokra", "homepage": "https://github.com/webpack/webpack-dev-middleware", "bugs": "https://github.com/webpack/webpack-dev-middleware/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/index.js", "types": "types/index.d.ts", "engines": {"node": ">= 18.12.0"}, "scripts": {"commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --cache --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell --cache --no-must-find-files --quiet \"**/*.*\"", "lint:types": "tsc --pretty --noEmit", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "clean": "del-cli dist types", "prebuild": "npm run clean", "build:types": "tsc --declaration --emitDeclarationOnly --outDir types && prettier \"types/**/*.ts\" --write", "build:code": "cross-env NODE_ENV=production babel src -d dist --copy-files", "build": "npm-run-all -p \"build:**\"", "test:only": "cross-env NODE_ENV=test jest", "test:watch": "npm run test:only -- --watch", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky && npm run build", "release": "standard-version"}, "files": ["dist", "types"], "peerDependencies": {"webpack": "^5.0.0"}, "peerDependenciesMeta": {"webpack": {"optional": true}}, "dependencies": {"colorette": "^2.0.10", "memfs": "^4.6.0", "mime-types": "^2.1.31", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.16.7", "@babel/core": "^7.16.7", "@babel/preset-env": "^7.16.7", "@commitlint/cli": "^19.0.3", "@commitlint/config-conventional": "^19.0.3", "@fastify/express": "^3.0.0", "@hapi/hapi": "^21.3.7", "@hono/node-server": "^1.12.0", "@types/connect": "^3.4.35", "@types/express": "^4.17.13", "@types/mime-types": "^2.1.1", "@types/node": "^22.3.0", "@types/on-finished": "^2.3.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.3.1", "chokidar": "^3.5.1", "connect": "^3.7.0", "cross-env": "^7.0.3", "cspell": "^8.3.2", "deepmerge": "^4.2.2", "del-cli": "^5.0.0", "eslint": "^8.28.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.25.4", "execa": "^5.1.1", "express": "^4.17.1", "fastify": "^4.26.2", "file-loader": "^6.2.0", "finalhandler": "^1.2.0", "hono": "^4.4.13", "husky": "^9.1.3", "jest": "^29.3.1", "joi": "^17.12.2", "koa": "^2.15.2", "lint-staged": "^15.2.0", "npm-run-all": "^4.1.5", "prettier": "^3.2.4", "router": "^1.3.8", "standard-version": "^9.3.0", "strip-ansi": "^6.0.0", "supertest": "^7.0.0", "typescript": "^5.3.3", "webpack": "^5.93.0"}, "keywords": ["webpack", "middleware", "development"]}