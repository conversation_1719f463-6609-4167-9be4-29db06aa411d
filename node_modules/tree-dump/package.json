{"name": "tree-dump", "private": false, "publishConfig": {"access": "public"}, "version": "1.0.3", "description": "Tree or binary tree printer, print any tree to terminal or debug window", "author": {"name": "<PERSON>ich", "url": "https://github.com/streamich"}, "license": "Apache-2.0", "homepage": "https://github.com/streamich/tree-dump", "repository": "streamich/tree-dump", "funding": {"type": "github", "url": "https://github.com/sponsors/streamich"}, "keywords": ["tree", "print", "dump", "tree dump", "print tree", "binary tree", "binary search tree"], "engines": {"node": ">=10.0"}, "main": "lib/index.js", "types": "lib/index.d.ts", "typings": "lib/index.d.ts", "files": ["LICENSE", "lib/"], "scripts": {"prettier": "npx prettier@3.2.5 --ignore-path .gitignore --write \"src/**/*.{ts,tsx,js,jsx}\"", "prettier:check": "npx prettier@3.2.5 --ignore-path .gitignore --list-different 'src/**/*.{ts,tsx,js,jsx}'", "lint": "npx tslint@6.1.3 'src/**/*.{js,jsx,ts,tsx}' -t verbose --project .", "clean": "npx rimraf@5.0.5 lib typedocs coverage gh-pages yarn-error.log", "build": "tsc --project tsconfig.build.json --module commonjs --target es2020 --outDir lib", "test": "jest --maxWorkers 7", "test:ci": "jest --maxWorkers 3 --no-cache", "coverage": "yarn test --collectCoverage", "typedoc": "npx typedoc@0.25.12", "build:pages": "npx rimraf@5.0.5 gh-pages && mkdir -p gh-pages && cp -r typedocs/* gh-pages && cp -r coverage gh-pages/coverage", "deploy:pages": "npx gh-pages -d gh-pages", "publish-coverage-and-typedocs": "yarn typedoc && yarn coverage && yarn build:pages && yarn deploy:pages"}, "peerDependencies": {"tslib": "2"}, "dependencies": {}, "devDependencies": {"@types/jest": "^29.5.12", "jest": "^29.7.0", "ts-jest": "^29.1.2", "tslib": "^2.6.2", "tslint-config-common": "^1.6.2", "typescript": "^5.4.4"}, "jest": {"verbose": true, "moduleFileExtensions": ["ts", "js"], "transform": {"^.+\\.ts$": "ts-jest"}, "transformIgnorePatterns": [], "testRegex": ".*/(__tests__|__jest__|demo)/.*\\.(test|spec)\\.ts$"}, "prettier": {"arrowParens": "always", "printWidth": 120, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "trailingComma": "all", "bracketSpacing": false}, "release": {"branches": ["master", "next"]}}