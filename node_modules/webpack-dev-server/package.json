{"name": "webpack-dev-server", "version": "5.2.1", "description": "Serves a webpack app. Updates the browser on changes.", "bin": "bin/webpack-dev-server.js", "main": "lib/Server.js", "types": "types/lib/Server.d.ts", "author": "<PERSON> @sokra", "bugs": "https://github.com/webpack/webpack-dev-server/issues", "homepage": "https://github.com/webpack/webpack-dev-server#readme", "repository": "https://github.com/webpack/webpack-dev-server", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "files": ["bin", "lib", "client", "types"], "engines": {"node": ">= 18.12.0"}, "scripts": {"fmt:check": "prettier \"{**/*,*}.{js,json,md,yml,css,ts}\" --list-different", "lint:prettier": "prettier --cache --list-different .", "lint:js": "eslint --cache .", "lint:types": "tsc --pretty --noEmit", "lint:spelling": "cspell --cache --no-must-find-files --quiet \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "commitlint": "commitlint --from=master", "build:client": "rimraf -g ./client/* && babel client-src/ --out-dir client/ --ignore \"client-src/webpack.config.js\" --ignore \"client-src/modules\" && webpack --config client-src/webpack.config.js", "build:types": "rimraf -g ./types/* && tsc --declaration --emitDeclarationOnly --outDir types && node ./scripts/extend-webpack-types.js && prettier \"types/**/*.ts\" --write && prettier \"types/**/*.ts\" --write", "build": "npm-run-all -p \"build:**\"", "test:only": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "test:coverage": "npm run test:only -- --coverage", "test:watch": "npm run test:coverage --watch", "test": "npm run test:coverage", "pretest": "npm run lint", "prepare": "husky && npm run build", "release": "standard-version"}, "dependencies": {"@types/bonjour": "^3.5.13", "@types/connect-history-api-fallback": "^1.5.4", "@types/express": "^4.17.21", "@types/express-serve-static-core": "^4.17.21", "@types/serve-index": "^1.9.4", "@types/serve-static": "^1.15.5", "@types/sockjs": "^0.3.36", "@types/ws": "^8.5.10", "ansi-html-community": "^0.0.8", "bonjour-service": "^1.2.1", "chokidar": "^3.6.0", "colorette": "^2.0.10", "compression": "^1.7.4", "connect-history-api-fallback": "^2.0.0", "express": "^4.21.2", "graceful-fs": "^4.2.6", "http-proxy-middleware": "^2.0.7", "ipaddr.js": "^2.1.0", "launch-editor": "^2.6.1", "open": "^10.0.3", "p-retry": "^6.2.0", "schema-utils": "^4.2.0", "selfsigned": "^2.4.1", "serve-index": "^1.9.1", "sockjs": "^0.3.24", "spdy": "^4.0.2", "webpack-dev-middleware": "^7.4.2", "ws": "^8.18.0"}, "devDependencies": {"@babel/cli": "^7.25.9", "@babel/core": "^7.25.9", "@babel/eslint-parser": "^7.25.9", "@babel/plugin-transform-object-assign": "^7.25.9", "@babel/plugin-transform-runtime": "^7.25.9", "@babel/preset-env": "^7.25.9", "@babel/runtime": "^7.25.9", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@hono/node-server": "^1.13.3", "@types/compression": "^1.7.2", "@types/node": "^22.8.4", "@types/node-forge": "^1.3.1", "@types/sockjs-client": "^1.5.1", "@types/trusted-types": "^2.0.2", "acorn": "^8.14.0", "babel-jest": "^29.5.0", "babel-loader": "^9.2.1", "body-parser": "^1.19.2", "connect": "^3.7.0", "core-js": "^3.38.1", "cspell": "^8.15.5", "css-loader": "^7.1.1", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-config-webpack": "^1.2.5", "eslint-plugin-import": "^2.31.0", "execa": "^5.1.1", "hono": "^4.6.8", "html-webpack-plugin": "^5.6.3", "http-proxy": "^1.18.1", "husky": "^9.1.6", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "klona": "^2.0.4", "less": "^4.1.1", "less-loader": "^12.1.0", "lint-staged": "^15.2.10", "marked": "^12.0.0", "memfs": "^4.14.0", "npm-run-all": "^4.1.5", "prettier": "^3.2.4", "puppeteer": "^23.6.1", "readable-stream": "^4.5.2", "require-from-string": "^2.0.2", "rimraf": "^5.0.5", "sockjs-client": "^1.6.1", "standard-version": "^9.3.0", "strip-ansi-v6": "npm:strip-ansi@^6.0.0", "style-loader": "^4.0.0", "supertest": "^7.0.0", "tcp-port-used": "^1.0.2", "typescript": "^5.7.2", "wait-for-expect": "^3.0.2", "webpack": "^5.94.0", "webpack-cli": "^5.0.1", "webpack-merge": "^6.0.1"}, "peerDependencies": {"webpack": "^5.0.0"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}, "webpack": {"optional": true}}}