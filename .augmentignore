# .augmentignore - Exclude files and directories from Augment's code analysis
# This file follows gitignore-style syntax

# ============================================================================
# Build artifacts and compiled files
# ============================================================================

# Webpack build output
dist/
build/
bundle.js
bundle.js.map

# React Native build outputs
android/app/build/
ios/build/
ios/DerivedData/

# Metro bundler cache
.metro-health-check*
metro-cache/

# Compiled files
*.o
*.so
*.dylib
*.class
*.jar
*.war
*.ear
*.nar
*.exe
*.dll
*.app
*.dSYM/

# ============================================================================
# Dependency directories and package files
# ============================================================================

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Package lock files (keep one, ignore others)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Bower dependencies
bower_components/

# ============================================================================
# IDE and editor files
# ============================================================================

# Visual Studio Code
.vscode/
*.code-workspace

# IntelliJ IDEA / WebStorm / Android Studio
.idea/
*.iml
*.ipr
*.iws
out/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Atom
.atom/

# ============================================================================
# Operating system files
# ============================================================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ============================================================================
# Log files and temporary files
# ============================================================================

# Log files
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output/

# Temporary files
*.tmp
*.temp
.cache/
.parcel-cache/

# ============================================================================
# Environment and configuration files with sensitive data
# ============================================================================

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Configuration files that might contain secrets
config/secrets.yml
config/database.yml
config/credentials.yml.enc
.secrets/

# Firebase config (if used)
.firebaserc
firebase-debug.log

# ============================================================================
# Test coverage reports and generated documentation
# ============================================================================

# Jest coverage reports
coverage/
.nyc_output/

# Documentation generators
docs/
doc/
.docusaurus/
.next/
.nuxt/
.vuepress/dist/

# Storybook build outputs
storybook-static/

# ============================================================================
# React Native specific
# ============================================================================

# React Native
.expo/
.expo-shared/
web-build/

# iOS
ios/Pods/
ios/*.xcworkspace/xcuserdata/
ios/*.xcodeproj/xcuserdata/
ios/*.xcodeproj/project.xcworkspace/xcuserdata/
ios/build/
ios/DerivedData/

# Android
android/.gradle/
android/app/build/
android/build/
android/gradle/
android/gradlew
android/gradlew.bat
android/local.properties
*.keystore
!debug.keystore

# ============================================================================
# Development and debugging
# ============================================================================

# Source maps (optional - uncomment if you don't want them analyzed)
# *.map

# Debug files
*.dSYM/
*.su
*.idb
*.pdb

# Profiling
*.prof

# ============================================================================
# Miscellaneous
# ============================================================================

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Large media files (optional - uncomment if you have large assets)
# *.mp4
# *.avi
# *.mov
# *.wmv
# *.flv
# *.webm

# Database files
*.sqlite
*.sqlite3
*.db

# Lock files for various tools
.lock-wscript
.wafpickle-*

# Optional npm cache directory
.npm/

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Stores VSCode versions used for testing VSCode extensions
.vscode-test/

# Temporary folders
tmp/
temp/
